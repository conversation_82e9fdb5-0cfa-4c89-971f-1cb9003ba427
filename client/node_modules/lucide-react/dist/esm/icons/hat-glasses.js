/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M14 18a2 2 0 0 0-4 0", key: "1v8fkw" }],
  [
    "path",
    {
      d: "m19 11-2.11-6.657a2 2 0 0 0-2.752-1.148l-1.276.61A2 2 0 0 1 12 4H8.5a2 2 0 0 0-1.925 1.456L5 11",
      key: "1fkr7p"
    }
  ],
  ["path", { d: "M2 11h20", key: "3eubbj" }],
  ["circle", { cx: "17", cy: "18", r: "3", key: "82mm0e" }],
  ["circle", { cx: "7", cy: "18", r: "3", key: "lvkj7j" }]
];
const HatGlasses = createLucideIcon("hat-glasses", __iconNode);

export { __iconNode, HatGlasses as default };
//# sourceMappingURL=hat-glasses.js.map
