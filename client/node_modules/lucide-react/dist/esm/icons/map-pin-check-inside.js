/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    {
      d: "M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",
      key: "1r0f0z"
    }
  ],
  ["path", { d: "m9 10 2 2 4-4", key: "1gnqz4" }]
];
const MapPinCheckInside = createLucideIcon("map-pin-check-inside", __iconNode);

export { __iconNode, MapPinCheckInside as default };
//# sourceMappingURL=map-pin-check-inside.js.map
