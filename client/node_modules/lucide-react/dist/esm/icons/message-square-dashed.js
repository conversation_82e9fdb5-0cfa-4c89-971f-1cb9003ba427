/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 19h.01", key: "1wutuc" }],
  ["path", { d: "M12 3h.01", key: "n36tog" }],
  ["path", { d: "M16 19h.01", key: "1vcnzz" }],
  ["path", { d: "M16 3h.01", key: "ll0zb8" }],
  ["path", { d: "M2 13h.01", key: "1aptou" }],
  [
    "path",
    { d: "M2 17v4.286a.71.71 0 0 0 1.212.502l2.202-2.202A2 2 0 0 1 6.828 19H8", key: "4cp7zq" }
  ],
  ["path", { d: "M2 5a2 2 0 0 1 2-2", key: "1iztiu" }],
  ["path", { d: "M2 9h.01", key: "1nzd1v" }],
  ["path", { d: "M20 3a2 2 0 0 1 2 2", key: "m48m3a" }],
  ["path", { d: "M22 13h.01", key: "ke7esy" }],
  ["path", { d: "M22 17a2 2 0 0 1-2 2", key: "17q5fo" }],
  ["path", { d: "M22 9h.01", key: "npkp49" }],
  ["path", { d: "M8 3h.01", key: "133hau" }]
];
const MessageSquareDashed = createLucideIcon("message-square-dashed", __iconNode);

export { __iconNode, MessageSquareDashed as default };
//# sourceMappingURL=message-square-dashed.js.map
