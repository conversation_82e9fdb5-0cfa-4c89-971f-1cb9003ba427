{"version": 3, "file": "DynamicIcon.js", "sources": ["../../src/DynamicIcon.ts"], "sourcesContent": ["'use client';\n\nimport { createElement, forwardRef, useEffect, useState } from 'react';\nimport { IconNode, LucideIcon, LucideProps } from './types';\nimport dynamicIconImports from './dynamicIconImports';\nimport Icon from './Icon';\n\nexport type DynamicIconModule = { default: LucideIcon; __iconNode: IconNode };\n\nexport type IconName = keyof typeof dynamicIconImports;\n\nexport const iconNames = Object.keys(dynamicIconImports) as Array<IconName>;\n\ninterface DynamicIconComponentProps extends LucideProps {\n  name: IconName;\n  fallback?: () => JSX.Element | null;\n}\n\nasync function getIconNode(name: IconName) {\n  if (!(name in dynamicIconImports)) {\n    throw new Error('[lucide-react]: Name in Lucide DynamicIcon not found');\n  }\n\n  // TODO: Replace this with a generic iconNode package.\n  const icon = (await dynamicIconImports[name]()) as DynamicIconModule;\n\n  return icon.__iconNode;\n}\n\n/**\n * Dynamic Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst DynamicIcon = forwardRef<SVGSVGElement, DynamicIconComponentProps>(\n  ({ name, fallback: Fallback, ...props }, ref) => {\n    const [iconNode, setIconNode] = useState<IconNode>();\n\n    useEffect(() => {\n      getIconNode(name)\n        .then(setIconNode)\n        .catch((error) => {\n          console.error(error);\n        });\n    }, [name]);\n\n    if (iconNode == null) {\n      if (Fallback == null) {\n        return null;\n      }\n\n      return createElement(Fallback);\n    }\n\n    return createElement(Icon, {\n      ref,\n      ...props,\n      iconNode,\n    });\n  },\n);\n\nexport default DynamicIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAWO;AAOP;AACE;AACE;AAAsE;AAIxE;AAEA;AACF;AAiBA;AAAoB;AAEhB;AAEA;AACE;AAGI;AAAmB;AACpB;AAGL;AACE;AACE;AAAO;AAGT;AAA6B;AAG/B;AAA2B;AACzB;AACG;AACH;AACD;AAEL;;"}