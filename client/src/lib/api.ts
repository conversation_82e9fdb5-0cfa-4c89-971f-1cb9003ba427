import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

export const api = axios.create({
  baseURL: API_BASE_URL,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Types
export interface User {
  id: string;
  email?: string;
  name: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
  oauthAccounts: OAuthAccount[];
}

export interface OAuthAccount {
  id: string;
  provider: 'google' | 'twitter' | 'telegram';
  providerUsername?: string;
  providerEmail?: string;
  createdAt: string;
}

export interface AuthResponse {
  user: User;
}

export interface LinkAccountResponse {
  success: boolean;
  message: string;
  account?: OAuthAccount;
  error?: string;
}

export interface UnlinkAccountResponse {
  success: boolean;
  message: string;
  error?: string;
}

export interface DeleteAccountResponse {
  success: boolean;
  message: string;
  error?: string;
}

export interface OAuthUrlResponse {
  url: string;
}

// API functions
export const authApi = {
  // Get current user
  getCurrentUser: async (): Promise<User> => {
    const response = await api.get<AuthResponse>('/auth/me');
    return response.data.user;
  },

  // Logout
  logout: async (): Promise<void> => {
    await api.post('/auth/logout');
  },

  // Get OAuth URLs
  getGoogleAuthUrl: async (redirectTo?: string): Promise<string> => {
    const response = await api.get<OAuthUrlResponse>('/auth/google', {
      params: { redirect_to: redirectTo },
    });
    return response.data.url;
  },

  getTwitterAuthUrl: async (redirectTo?: string): Promise<string> => {
    const response = await api.get<OAuthUrlResponse>('/auth/twitter', {
      params: { redirect_to: redirectTo },
    });
    return response.data.url;
  },

  getTelegramAuthUrl: async (redirectTo?: string): Promise<string> => {
    const response = await api.get<OAuthUrlResponse>('/auth/telegram', {
      params: { redirect_to: redirectTo },
    });
    return response.data.url;
  },
};

export const userApi = {
  // Get user profile
  getProfile: async (): Promise<User> => {
    const response = await api.get<AuthResponse>('/user/profile');
    return response.data.user;
  },

  // Link OAuth account
  linkAccount: async (provider: string, code: string): Promise<LinkAccountResponse> => {
    const response = await api.post<LinkAccountResponse>(`/user/link/${provider}`, null, {
      params: { code },
    });
    return response.data;
  },

  // Unlink OAuth account
  unlinkAccount: async (provider: string): Promise<UnlinkAccountResponse> => {
    const response = await api.delete<UnlinkAccountResponse>(`/user/unlink/${provider}`);
    return response.data;
  },

  // Delete account
  deleteAccount: async (): Promise<DeleteAccountResponse> => {
    const response = await api.delete<DeleteAccountResponse>('/user/account');
    return response.data;
  },

  // Get OAuth URLs for linking
  getLinkUrl: async (provider: string, redirectTo?: string): Promise<string> => {
    const response = await api.get<OAuthUrlResponse>(`/user/link/${provider}/url`, {
      params: { redirect_to: redirectTo },
    });
    return response.data.url;
  },
};
